# DemoContainer 测试页面

这是一个测试页面，用于验证改进后的 DemoContainer 组件。

## 基础示例

<DemoContainer>
  <template #demo>
    <div style="padding: 20px; text-align: center; border: 2px dashed #42b883; border-radius: 8px;">
      <h3 style="color: #42b883; margin: 0 0 16px 0;">Hello Vue 3!</h3>
      <button style="padding: 8px 16px; background: #42b883; color: white; border: none; border-radius: 4px; cursor: pointer;">
        点击测试
      </button>
      <p style="margin: 16px 0 0 0; color: #666;">这是一个简单的示例组件</p>
    </div>
  </template>
  
  <template #code>
```vue
<template>
  <div class="demo-wrapper">
    <h3>{{ title }}</h3>
    <button @click="handleClick" :class="buttonClass">
      {{ buttonText }}
    </button>
    <p v-if="clicked">{{ message }}</p>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const title = ref('Hello Vue 3!')
const buttonText = ref('点击测试')
const clicked = ref(false)
const clickCount = ref(0)

const buttonClass = computed(() => ({
  'demo-button': true,
  'clicked': clicked.value
}))

const message = computed(() => 
  `按钮已被点击 ${clickCount.value} 次!`
)

const handleClick = () => {
  clicked.value = true
  clickCount.value++
  buttonText.value = `点击了 ${clickCount.value} 次`
}
</script>

<style scoped>
.demo-wrapper {
  padding: 20px;
  text-align: center;
  border: 2px dashed #42b883;
  border-radius: 8px;
}

.demo-wrapper h3 {
  color: #42b883;
  margin: 0 0 16px 0;
}

.demo-button {
  padding: 8px 16px;
  background: #42b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.demo-button:hover {
  background: #369870;
  transform: translateY(-2px);
}

.demo-button.clicked {
  background: #ff6b6b;
}

.demo-wrapper p {
  margin: 16px 0 0 0;
  color: #666;
  font-weight: bold;
}
</style>
```
  </template>
</DemoContainer>

## 功能说明

### ✨ **新增功能**

1. **在线编辑模式**
   - 点击"在线编辑"按钮进入编辑模式
   - 提供简化的代码编辑器
   - 支持 Tab 键缩进

2. **Vue Playground 集成**
   - 点击"在新窗口打开"按钮
   - 在 Vue 官方 Playground 中打开代码
   - 享受完整的在线编辑体验

3. **代码复制功能**
   - 一键复制代码到剪贴板
   - 复制成功后显示确认提示

### 🛠️ **使用方式**

1. **查看代码**：点击"查看代码"按钮
2. **在线编辑**：点击"在线编辑"标签页
3. **外部编辑**：点击"在新窗口打开"在 Vue Playground 中编辑
4. **复制代码**：点击复制按钮获取代码

### 🎯 **技术特点**

- **渐进增强**：从简单的代码查看到完整的在线编辑
- **外部集成**：与 Vue 官方 Playground 无缝集成
- **用户友好**：提供多种编辑方式满足不同需求
- **稳定可靠**：避免了复杂的本地编译，减少出错可能

这种方案既保持了简单性，又提供了强大的编辑功能！
