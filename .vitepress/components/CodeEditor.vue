<template>
  <div class="code-editor-container">
    <!-- 编辑器头部 -->
    <div class="editor-header">
      <div class="editor-title">
        <svg width="16" height="16" viewBox="0 0 24 24">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        Vue REPL 编辑器
      </div>
      <div class="editor-actions">
        <button class="action-btn" @click="resetCode" title="重置代码">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
          </svg>
        </button>
        <button class="action-btn" @click="copyCode" title="复制代码">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
        </button>
        <button class="action-btn close-btn" @click="$emit('close')" title="关闭编辑器">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Vue REPL 编辑器 -->
    <div class="editor-content">
      <Repl
        v-if="store"
        :store="store"
        :show-compile-output="false"
        :show-import-map="false"
        :clear-console="false"
        :layout="'horizontal'"
        :ssr="false"
        class="vue-repl-editor"
      />
      <div v-else-if="initError" class="error-editor">
        <div class="error-icon">⚠️</div>
        <h3>编辑器初始化失败</h3>
        <p>{{ initError }}</p>
        <button @click="retryInit" class="retry-btn">重试</button>
      </div>
      <div v-else class="loading-editor">
        <div class="loading-spinner"></div>
        <p>正在初始化 Vue REPL 编辑器...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { Repl } from '@vue/repl'
import '@vue/repl/style.css'

// Props
const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
defineEmits(['close'])

// 响应式数据
const store = ref(null)
const originalCode = ref('')
const initError = ref('')

// 初始化 Vue REPL Store
const initStore = async () => {
  try {
    console.log('开始初始化 Vue REPL Store...')

    // 动态导入，尝试不同的导入方式
    let ReplStore
    try {
      // 方式1: 尝试命名导入
      const replModule = await import('@vue/repl')
      ReplStore = replModule.ReplStore || replModule.default?.ReplStore
    } catch (e) {
      console.warn('命名导入失败，尝试默认导入:', e)
      // 方式2: 尝试默认导入
      const replModule = await import('@vue/repl')
      ReplStore = replModule.default
    }

    if (!ReplStore) {
      throw new Error('无法导入 ReplStore')
    }

    // 创建 REPL Store
    const replStore = new ReplStore({
      showOutput: true,
      outputMode: 'preview'
    })

    // 设置 Vue 版本
    await replStore.setVueVersion('3.4.0')

    // 设置 import map
    replStore.setImportMap({
      imports: {
        'vue': 'https://unpkg.com/vue@3.4.0/dist/vue.esm-browser.js'
      }
    })

    // 设置初始文件
    const code = props.code || getDefaultCode()
    originalCode.value = code

    // 清除默认文件并添加我们的文件
    replStore.files = {}
    replStore.addFile('App.vue', code)
    replStore.setActive('App.vue')

    // 添加 helper-components 支持
    addHelperComponents(replStore)

    store.value = replStore
    console.log('Vue REPL Store 初始化成功')
  } catch (error) {
    console.error('初始化 Vue REPL 失败:', error)
    console.error('错误详情:', error.message)
    // 如果 REPL 初始化失败，显示错误信息
    initError.value = `初始化失败: ${error.message}`
    store.value = null
  }
}

// 重试初始化
const retryInit = () => {
  initError.value = ''
  store.value = null
  initStore()
}

// 添加 helper-components 支持
const addHelperComponents = (replStore) => {
  // 添加组件库的类型定义文件
  replStore.addFile('helper-components.d.ts', `
declare module 'helper-components' {
  import { DefineComponent } from 'vue'

  export const HelperDialog: DefineComponent<any, any, any>
  export const HelperImage: DefineComponent<any, any, any>
  export const HelperToast: DefineComponent<any, any, any>
  export const ButtonCopy: DefineComponent<any, any, any>
  export const Loading: DefineComponent<any, any, any>
}
`)

  // 添加组件库模拟文件
  replStore.addFile('helper-components.js', `
// Helper Components 模拟实现
import { defineComponent, h } from 'vue'

export const HelperDialog = defineComponent({
  name: 'HelperDialog',
  props: {
    visible: Boolean,
    title: String,
    content: String
  },
  setup(props, { slots }) {
    return () => props.visible ? h('div', {
      style: {
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        background: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        zIndex: 1000,
        border: '1px solid #ddd'
      }
    }, [
      h('h3', { style: { margin: '0 0 12px 0' } }, props.title || '对话框'),
      h('p', { style: { margin: '0 0 16px 0' } }, props.content || '这是一个模拟的 HelperDialog 组件'),
      slots.default?.()
    ]) : null
  }
})

export const HelperToast = defineComponent({
  name: 'HelperToast',
  props: {
    message: String,
    type: { type: String, default: 'info' }
  },
  setup(props) {
    const colors = {
      info: '#409eff',
      success: '#67c23a',
      warning: '#e6a23c',
      error: '#f56c6c'
    }

    return () => h('div', {
      style: {
        padding: '12px 16px',
        background: colors[props.type] || colors.info,
        color: 'white',
        borderRadius: '4px',
        margin: '8px 0'
      }
    }, props.message || '这是一个模拟的 HelperToast 组件')
  }
})

export const ButtonCopy = defineComponent({
  name: 'ButtonCopy',
  props: {
    text: String
  },
  setup(props) {
    const handleCopy = () => {
      navigator.clipboard?.writeText(props.text || '复制内容')
      alert('复制成功！')
    }

    return () => h('button', {
      onClick: handleCopy,
      style: {
        padding: '8px 16px',
        background: '#409eff',
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer'
      }
    }, '复制')
  }
})
`)
}

// 获取默认代码
const getDefaultCode = () => {
  return `<template>
  <div class="demo-container">
    <h3>Hello Vue REPL!</h3>
    <p>{{ message }}</p>
    <button @click="handleClick">{{ buttonText }}</button>

    <div v-if="showExtra" class="extra-content">
      <HelperToast message="这是一个成功提示" type="success" />
      <ButtonCopy :text="copyText" />
    </div>

    <button @click="showExtra = !showExtra">
      {{ showExtra ? '隐藏' : '显示' }}组件
    </button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { HelperToast, ButtonCopy } from './helper-components.js'

const message = ref('这是一个 Vue REPL 示例')
const buttonText = ref('点击我')
const showExtra = ref(false)
const copyText = ref('Hello Vue REPL!')

const handleClick = () => {
  message.value = '按钮被点击了！'
  buttonText.value = '再次点击'
}
<\/script>

<style scoped>
.demo-container {
  padding: 20px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin: 4px;
}

button:hover {
  background: #66b1ff;
}

.extra-content {
  margin: 16px 0;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}
</style>`
}

// 重置代码
const resetCode = () => {
  if (store.value && originalCode.value) {
    store.value.files = {}
    store.value.addFile('App.vue', originalCode.value)
    store.value.setActive('App.vue')
    addHelperComponents(store.value)
  }
}

// 复制代码
const copyCode = async () => {
  try {
    const activeFile = store.value?.state.activeFile
    if (activeFile) {
      await navigator.clipboard.writeText(activeFile.code)
      // 显示复制成功提示
      const btn = document.querySelector('[title="复制代码"]')
      if (btn) {
        const originalTitle = btn.title
        btn.title = '✅ 复制成功!'
        setTimeout(() => {
          btn.title = originalTitle
        }, 2000)
      }
    }
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 监听 props.code 变化
watch(() => props.code, (newCode) => {
  if (newCode && store.value) {
    console.log('代码更新:', newCode)
    originalCode.value = newCode
    store.value.files = {}
    store.value.addFile('App.vue', newCode)
    store.value.setActive('App.vue')
    addHelperComponents(store.value)
  }
}, { immediate: false })

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible && !store.value) {
    initStore()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    initStore()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    initCode()
  }
})
</script>

<style scoped>
.code-editor-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--vp-c-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--vp-c-text-1);
  font-size: 16px;
}

.editor-title svg {
  color: rgb(94, 130, 192);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 6px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-text-1);
  border-color: rgb(94, 130, 192);
}

.close-btn:hover {
  background: #fee;
  color: #c33;
  border-color: #fcc;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

.editor-layout {
  display: flex;
  height: 100%;
}

.editor-panel,
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--vp-c-divider);
}

.preview-panel {
  border-right: none;
}

.panel-header {
  padding: 8px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  font-size: 12px;
  font-weight: 500;
  color: var(--vp-c-text-2);
}

.code-editor {
  flex: 1;
  position: relative;
}

.code-textarea {
  width: 100%;
  height: 100%;
  padding: 16px;
  border: none;
  outline: none;
  background: var(--vp-code-block-bg);
  color: var(--vp-c-text-1);
  font-family: var(--vp-font-family-mono);
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  tab-size: 2;
}

.preview-content {
  flex: 1;
  position: relative;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: var(--vp-c-bg);
  border-radius: 4px;
}

/* 加载和错误状态样式 */
.loading-editor,
.error-editor {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--vp-c-text-2);
  padding: 40px 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--vp-c-border);
  border-top: 3px solid rgb(94, 130, 192);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.error-editor {
  color: var(--vp-c-text-1);
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-editor h3 {
  margin: 0 0 12px 0;
  color: var(--vp-c-text-1);
}

.error-editor p {
  margin: 0 0 20px 0;
  color: var(--vp-c-text-2);
  max-width: 400px;
  line-height: 1.5;
}

.retry-btn {
  padding: 8px 16px;
  background: rgb(94, 130, 192);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: rgb(74, 110, 172);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 覆盖 Vue REPL 的默认样式 */
:deep(.vue-repl) {
  height: 100% !important;
  border: none !important;
}

:deep(.vue-repl .split-pane) {
  height: 100% !important;
}

:deep(.vue-repl .editor-container) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container) {
  background: var(--vp-c-bg) !important;
}

:deep(.vue-repl .file-selector) {
  background: var(--vp-c-bg-soft) !important;
  border-bottom: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file) {
  color: var(--vp-c-text-2) !important;
  border-right: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file.active) {
  background: var(--vp-c-bg) !important;
  color: var(--vp-c-text-1) !important;
}

:deep(.vue-repl .monaco-editor) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container iframe) {
  background: white !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    padding: 8px 12px;
  }

  .editor-title {
    font-size: 14px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  :deep(.vue-repl) {
    --split-pane-min-size: 100px !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :deep(.vue-repl .output-container iframe) {
    background: #1a1a1a !important;
  }
}
</style>
