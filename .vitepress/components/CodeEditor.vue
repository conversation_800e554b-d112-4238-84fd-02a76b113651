<template>
  <div class="code-editor-container">
    <!-- 简单的头部 -->
    <div class="editor-header">
      <div class="editor-title">代码编辑器</div>
      <button class="close-btn" @click="$emit('close')" title="关闭">×</button>
    </div>

    <!-- 分屏编辑器 -->
    <div class="editor-content">
      <!-- 左侧：代码编辑 -->
      <div class="editor-panel">
        <div class="panel-header">代码编辑</div>
        <textarea
          ref="codeTextarea"
          v-model="editableCode"
          class="code-textarea"
          spellcheck="false"
          @input="updatePreview"
        />
      </div>

      <!-- 右侧：实时预览 -->
      <div class="preview-panel">
        <div class="panel-header">实时预览</div>
        <iframe
          ref="previewFrame"
          class="preview-iframe"
          sandbox="allow-scripts allow-same-origin"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

// Props
const props = defineProps({
  code: {
    type: String,
    default: ''
  }
})

// Emits
defineEmits(['close'])

// 响应式数据
const editableCode = ref('')
const previewFrame = ref(null)

// 更新预览
const updatePreview = () => {
  if (!previewFrame.value) return

  const code = editableCode.value
  const templateMatch = code.match(/<template[^>]*>([\s\S]*?)<\/template>/i)
  const scriptMatch = code.match(/<script[^>]*>([\s\S]*?)<\/script>/i)
  const styleMatch = code.match(/<style[^>]*>([\s\S]*?)<\/style>/i)

  let template = templateMatch ? templateMatch[1].trim() : ''
  let script = scriptMatch ? scriptMatch[1].trim() : ''
  let style = styleMatch ? styleMatch[1].trim() : ''

  // 移除 import 语句
  script = script.replace(/import\s+.*?from\s+['"].*?['"];?\s*/g, '')

  const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <script src="https://unpkg.com/vue@3.4.0/dist/vue.global.js"><\/script>
  <style>
    body { margin: 0; padding: 16px; font-family: system-ui, sans-serif; }
    ${style}
  </style>
</head>
<body>
  <div id="app"></div>
  <script>
    try {
      const { createApp, ref, reactive, computed, watch, onMounted } = Vue;

      const component = {
        template: \`${template.replace(/`/g, '\\`')}\` || '<div>请添加模板内容</div>',
        setup() {
          ${script}

          // 自动返回所有响应式变量
          const result = {};
          const vars = ['message', 'buttonText', 'showDialog', 'handleClick', 'status', 'info', 'handleTest', 'count', 'showExtra', 'description'];
          vars.forEach(name => {
            try {
              if (typeof eval(name) !== 'undefined') result[name] = eval(name);
            } catch (e) {}
          });
          return result;
        }
      };

      createApp(component).mount('#app');
    } catch (error) {
      document.getElementById('app').innerHTML = '<div style="color: red; padding: 20px;">错误: ' + error.message + '</div>';
    }
  <\/script>
</body>
</html>`;

  previewFrame.value.srcdoc = html
}

// 初始化代码
const initCode = () => {
  console.log('初始化代码，接收到的 props.code:', props.code)
  const code = props.code || getDefaultCode()
  originalCode.value = code
  editableCode.value = code
  console.log('设置的可编辑代码:', editableCode.value)
  updatePreview()
}

// 获取默认代码
const getDefaultCode = () => {
  return `<template>
  <div class="demo-container">
    <h3>Hello Vue REPL!</h3>
    <p>{{ message }}</p>
    <button @click="handleClick">{{ buttonText }}</button>

    <div v-if="showExtra" class="extra-content">
      <HelperToast message="这是一个成功提示" type="success" />
      <ButtonCopy :text="copyText" />
    </div>

    <button @click="showExtra = !showExtra">
      {{ showExtra ? '隐藏' : '显示' }}组件
    </button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { HelperToast, ButtonCopy } from './helper-components.js'

const message = ref('这是一个 Vue REPL 示例')
const buttonText = ref('点击我')
const showExtra = ref(false)
const copyText = ref('Hello Vue REPL!')

const handleClick = () => {
  message.value = '按钮被点击了！'
  buttonText.value = '再次点击'
}
<\/script>

<style scoped>
.demo-container {
  padding: 20px;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin: 4px;
}

button:hover {
  background: #66b1ff;
}

.extra-content {
  margin: 16px 0;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 4px;
}
</style>`
}

// 代码变化处理
const onCodeChange = () => {
  updatePreview()
}

// 键盘事件处理
const onKeyDown = (e) => {
  if (e.key === 'Tab') {
    e.preventDefault()
    const start = e.target.selectionStart
    const end = e.target.selectionEnd
    e.target.value = e.target.value.substring(0, start) + '  ' + e.target.value.substring(end)
    e.target.selectionStart = e.target.selectionEnd = start + 2
    editableCode.value = e.target.value
  }
}

// 格式化代码
const formatCode = () => {
  if (!editableCode.value) return

  const lines = editableCode.value.split('\n')
  let indentLevel = 0
  const indentSize = 2

  const formattedLines = lines.map(line => {
    const trimmed = line.trim()
    if (!trimmed) return ''

    // 减少缩进
    if (trimmed.startsWith('</') || trimmed.startsWith('}')) {
      indentLevel = Math.max(0, indentLevel - 1)
    }

    const result = ' '.repeat(indentLevel * indentSize) + trimmed

    // 增加缩进
    if ((trimmed.includes('<') && !trimmed.includes('</') && !trimmed.endsWith('/>')) ||
        trimmed.endsWith('{') || trimmed.endsWith('(')) {
      indentLevel++
    }

    return result
  })

  editableCode.value = formattedLines.join('\n')
  updatePreview()
}

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(editableCode.value)
    const btn = document.querySelector('[title="复制代码"]')
    if (btn) {
      const originalTitle = btn.title
      btn.title = '✅ 复制成功!'
      setTimeout(() => {
        btn.title = originalTitle
      }, 2000)
    }
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 在 Vue Playground 中打开
const openInPlayground = () => {
  const code = encodeURIComponent(editableCode.value)
  const playgroundUrl = `https://play.vuejs.org/#${code}`
  window.open(playgroundUrl, '_blank')
}

// 监听 props.code 变化
watch(() => props.code, (newCode) => {
  if (newCode) {
    console.log('代码更新:', newCode)
    originalCode.value = newCode
    editableCode.value = newCode
    updatePreview()
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  initCode()
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    initCode()
  }
})
</script>

<style scoped>
.code-editor-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--vp-c-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--vp-c-text-1);
  font-size: 16px;
}

.editor-title svg {
  color: rgb(94, 130, 192);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 6px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-text-1);
  border-color: rgb(94, 130, 192);
}

.close-btn:hover {
  background: #fee;
  color: #c33;
  border-color: #fcc;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

.editor-layout {
  display: flex;
  height: 100%;
}

.editor-panel,
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--vp-c-divider);
}

.preview-panel {
  border-right: none;
}

.panel-header {
  padding: 8px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  font-size: 12px;
  font-weight: 500;
  color: var(--vp-c-text-2);
}

.code-editor {
  flex: 1;
  position: relative;
}

.code-textarea {
  width: 100%;
  height: 100%;
  padding: 16px;
  border: none;
  outline: none;
  background: var(--vp-code-block-bg);
  color: var(--vp-c-text-1);
  font-family: var(--vp-font-family-mono);
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  tab-size: 2;
}

.preview-content {
  flex: 1;
  position: relative;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: var(--vp-c-bg);
  border-radius: 4px;
}

/* 加载和错误状态样式 */
.loading-editor,
.error-editor {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--vp-c-text-2);
  padding: 40px 20px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--vp-c-border);
  border-top: 3px solid rgb(94, 130, 192);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.error-editor {
  color: var(--vp-c-text-1);
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.error-editor h3 {
  margin: 0 0 12px 0;
  color: var(--vp-c-text-1);
}

.error-editor p {
  margin: 0 0 20px 0;
  color: var(--vp-c-text-2);
  max-width: 400px;
  line-height: 1.5;
}

.retry-btn {
  padding: 8px 16px;
  background: rgb(94, 130, 192);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.retry-btn:hover {
  background: rgb(74, 110, 172);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 覆盖 Vue REPL 的默认样式 */
:deep(.vue-repl) {
  height: 100% !important;
  border: none !important;
}

:deep(.vue-repl .split-pane) {
  height: 100% !important;
}

:deep(.vue-repl .editor-container) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container) {
  background: var(--vp-c-bg) !important;
}

:deep(.vue-repl .file-selector) {
  background: var(--vp-c-bg-soft) !important;
  border-bottom: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file) {
  color: var(--vp-c-text-2) !important;
  border-right: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file.active) {
  background: var(--vp-c-bg) !important;
  color: var(--vp-c-text-1) !important;
}

:deep(.vue-repl .monaco-editor) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container iframe) {
  background: white !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    padding: 8px 12px;
  }

  .editor-title {
    font-size: 14px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  :deep(.vue-repl) {
    --split-pane-min-size: 100px !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :deep(.vue-repl .output-container iframe) {
    background: #1a1a1a !important;
  }
}
</style>
