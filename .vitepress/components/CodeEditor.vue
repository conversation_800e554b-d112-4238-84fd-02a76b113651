<template>
  <div class="code-editor-container">
    <!-- 编辑器头部 -->
    <div class="editor-header">
      <div class="editor-title">
        <svg width="16" height="16" viewBox="0 0 24 24">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        代码编辑器
      </div>
      <div class="editor-actions">
        <button class="action-btn" @click="formatCode" title="格式化代码">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M4 3h16c.55 0 1 .45 1 1s-.45 1-1 1H4c-.55 0-1-.45-1-1s.45-1 1-1zm0 4h16c.55 0 1 .45 1 1s-.45 1-1 1H4c-.55 0-1-.45-1-1s.45-1 1-1zm0 4h16c.55 0 1 .45 1 1s-.45 1-1 1H4c-.55 0-1-.45-1-1s.45-1 1-1zm0 4h16c.55 0 1 .45 1 1s-.45 1-1 1H4c-.55 0-1-.45-1-1s.45-1 1-1z"/>
          </svg>
        </button>
        <button class="action-btn" @click="copyCode" title="复制代码">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
        </button>
        <button class="action-btn" @click="openInPlayground" title="在 Vue Playground 中打开">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"/>
          </svg>
        </button>
        <button class="action-btn close-btn" @click="$emit('close')" title="关闭编辑器">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 分屏编辑器 -->
    <div class="editor-content">
      <div class="editor-layout">
        <!-- 左侧：代码编辑器 -->
        <div class="editor-panel">
          <div class="panel-header">
            <span class="panel-title">📝 代码编辑</span>
          </div>
          <div class="code-editor">
            <textarea
              ref="codeTextarea"
              v-model="editableCode"
              class="code-textarea"
              spellcheck="false"
              @keydown="onKeyDown"
              @input="onCodeChange"
            />
          </div>
        </div>

        <!-- 右侧：预览面板 -->
        <div class="preview-panel">
          <div class="panel-header">
            <span class="panel-title">👁️ 实时预览</span>
          </div>
          <div class="preview-content">
            <iframe
              ref="previewFrame"
              class="preview-iframe"
              sandbox="allow-scripts allow-same-origin"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

// Props
const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
defineEmits(['close'])

// 响应式数据
const editableCode = ref('')
const originalCode = ref('')
const codeTextarea = ref(null)
const previewFrame = ref(null)

// 更新预览 - 需要先定义这个函数
const updatePreview = () => {
  if (!previewFrame.value) return

  const html = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Vue Preview</title>
  <script src="https://unpkg.com/vue@3.4.0/dist/vue.global.js"><\/script>
  <style>
    body { margin: 0; padding: 16px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
  </style>
</head>
<body>
  <div id="app"></div>
  <script>
    try {
      const { createApp, ref } = Vue;

      // 解析 Vue SFC
      const code = \`${editableCode.value.replace(/`/g, '\\`')}\`;

      // 简单的 SFC 解析
      const templateMatch = code.match(/<template[^>]*>([\\s\\S]*?)<\\/template>/i);
      const scriptMatch = code.match(/<script[^>]*>([\\s\\S]*?)<\\/script>/i);
      const styleMatch = code.match(/<style[^>]*>([\\s\\S]*?)<\\/style>/i);

      if (templateMatch) {
        const template = templateMatch[1].trim();
        let script = '';

        if (scriptMatch) {
          script = scriptMatch[1].trim();
          // 移除 import 语句
          script = script.replace(/import\\s+.*?from\\s+['"].*?['"];?\\s*/g, '');
        }

        // 添加样式
        if (styleMatch) {
          const style = document.createElement('style');
          style.textContent = styleMatch[1];
          document.head.appendChild(style);
        }

        // 创建组件
        const component = {
          template: template,
          setup() {
            ${script.replace(/const\\s+/g, 'const ').replace(/export\\s+default\\s+{[^}]*}/g, '')}

            // 返回所有响应式数据
            return {
              message: typeof message !== 'undefined' ? message : ref('Hello'),
              buttonText: typeof buttonText !== 'undefined' ? buttonText : ref('Button'),
              showDialog: typeof showDialog !== 'undefined' ? showDialog : ref(false),
              imageUrl: typeof imageUrl !== 'undefined' ? imageUrl : ref(''),
              handleCopy: typeof handleCopy !== 'undefined' ? handleCopy : () => {},
              showExtra: typeof showExtra !== 'undefined' ? showExtra : ref(false),
              description: typeof description !== 'undefined' ? description : ref(''),
              handleClick: typeof handleClick !== 'undefined' ? handleClick : () => {}
            };
          }
        };

        createApp(component).mount('#app');
      } else {
        document.getElementById('app').innerHTML = '<p style="color: #999;">请添加 &lt;template&gt; 标签</p>';
      }
    } catch (error) {
      document.getElementById('app').innerHTML = '<p style="color: #f56c6c;">预览错误: ' + error.message + '</p>';
      console.error('Preview error:', error);
    }
  <\/script>
</body>
</html>`;

  previewFrame.value.srcdoc = html
}

// 初始化代码
const initCode = () => {
  console.log('初始化代码，接收到的 props.code:', props.code) // 调试日志
  const code = props.code || getDefaultCode()
  originalCode.value = code
  editableCode.value = code
  console.log('设置的可编辑代码:', editableCode.value) // 调试日志
  updatePreview()
}

// 监听 props.code 变化
watch(() => props.code, (newCode) => {
  if (newCode) {
    console.log('代码更新:', newCode) // 调试日志
    originalCode.value = newCode
    editableCode.value = newCode
    updatePreview()
  }
}, { immediate: true })

// 获取默认代码
const getDefaultCode = () => {
  return `<template>
  <div class="demo-container">
    <h2>Helper Components 示例</h2>

    <div class="component-showcase">
      <div class="helper-toast success">
        ✅ {{ message }}
      </div>

      <button class="copy-btn" @click="handleCopy">
        {{ buttonText }}
      </button>

      <img
        :src="imageUrl"
        alt="示例图片"
        class="helper-image"
      />

      <button @click="showDialog = !showDialog">
        {{ showDialog ? '隐藏' : '显示' }}对话框
      </button>

      <div v-if="showDialog" class="helper-dialog">
        <h3>示例对话框</h3>
        <p>这是一个使用 helper-components 的示例</p>
        <button @click="showDialog = false">关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const message = ref('这是一个成功提示')
const buttonText = ref('复制按钮')
const showDialog = ref(false)
const imageUrl = ref('https://via.placeholder.com/200x150?text=示例图片')

const handleCopy = () => {
  buttonText.value = '复制成功！'
  setTimeout(() => {
    buttonText.value = '复制按钮'
  }, 2000)
}
<\/script>

<style scoped>
.demo-container {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.component-showcase {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 400px;
}

.helper-toast {
  padding: 12px 16px;
  border-radius: 4px;
  margin: 8px 0;
}

.helper-toast.success {
  background: #67c23a;
  color: white;
}

.copy-btn, button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.copy-btn:hover, button:hover {
  background: #66b1ff;
}

.helper-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.helper-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1000;
  border: 1px solid #ddd;
}
</style>`
}



// 代码变化处理
const onCodeChange = () => {
  updatePreview()
}

// 键盘事件处理
const onKeyDown = (e) => {
  if (e.key === 'Tab') {
    e.preventDefault()
    const start = e.target.selectionStart
    const end = e.target.selectionEnd
    e.target.value = e.target.value.substring(0, start) + '  ' + e.target.value.substring(end)
    e.target.selectionStart = e.target.selectionEnd = start + 2
    editableCode.value = e.target.value
  }
}

// 格式化代码
const formatCode = () => {
  if (!editableCode.value) return

  const lines = editableCode.value.split('\n')
  let indentLevel = 0
  const indentSize = 2

  const formattedLines = lines.map(line => {
    const trimmed = line.trim()
    if (!trimmed) return ''

    // 减少缩进
    if (trimmed.startsWith('</') || trimmed.startsWith('}')) {
      indentLevel = Math.max(0, indentLevel - 1)
    }

    const result = ' '.repeat(indentLevel * indentSize) + trimmed

    // 增加缩进
    if ((trimmed.includes('<') && !trimmed.includes('</') && !trimmed.endsWith('/>')) ||
        trimmed.endsWith('{') || trimmed.endsWith('(')) {
      indentLevel++
    }

    return result
  })

  editableCode.value = formattedLines.join('\n')
  updatePreview()
}

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(editableCode.value)
    const btn = document.querySelector('[title="复制代码"]')
    if (btn) {
      const originalTitle = btn.title
      btn.title = '✅ 复制成功!'
      setTimeout(() => {
        btn.title = originalTitle
      }, 2000)
    }
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 在 Vue Playground 中打开
const openInPlayground = () => {
  const code = encodeURIComponent(editableCode.value)
  const playgroundUrl = `https://play.vuejs.org/#${code}`
  window.open(playgroundUrl, '_blank')
}

// 生命周期
onMounted(() => {
  if (props.visible) {
    initCode()
  }
})
</script>

<style scoped>
.code-editor-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--vp-c-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--vp-c-text-1);
  font-size: 16px;
}

.editor-title svg {
  color: rgb(94, 130, 192);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 6px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-text-1);
  border-color: rgb(94, 130, 192);
}

.close-btn:hover {
  background: #fee;
  color: #c33;
  border-color: #fcc;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

.editor-layout {
  display: flex;
  height: 100%;
}

.editor-panel,
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--vp-c-divider);
}

.preview-panel {
  border-right: none;
}

.panel-header {
  padding: 8px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  font-size: 12px;
  font-weight: 500;
  color: var(--vp-c-text-2);
}

.code-editor {
  flex: 1;
  position: relative;
}

.code-textarea {
  width: 100%;
  height: 100%;
  padding: 16px;
  border: none;
  outline: none;
  background: var(--vp-code-block-bg);
  color: var(--vp-c-text-1);
  font-family: var(--vp-font-family-mono);
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  tab-size: 2;
}

.preview-content {
  flex: 1;
  position: relative;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 覆盖 Vue REPL 的默认样式 */
:deep(.vue-repl) {
  height: 100% !important;
  border: none !important;
}

:deep(.vue-repl .split-pane) {
  height: 100% !important;
}

:deep(.vue-repl .editor-container) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container) {
  background: var(--vp-c-bg) !important;
}

:deep(.vue-repl .file-selector) {
  background: var(--vp-c-bg-soft) !important;
  border-bottom: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file) {
  color: var(--vp-c-text-2) !important;
  border-right: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file.active) {
  background: var(--vp-c-bg) !important;
  color: var(--vp-c-text-1) !important;
}

:deep(.vue-repl .monaco-editor) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container iframe) {
  background: white !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    padding: 8px 12px;
  }

  .editor-title {
    font-size: 14px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  :deep(.vue-repl) {
    --split-pane-min-size: 100px !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :deep(.vue-repl .output-container iframe) {
    background: #1a1a1a !important;
  }
}
</style>
