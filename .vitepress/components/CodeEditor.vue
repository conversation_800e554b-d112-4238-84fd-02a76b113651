<template>
  <div class="code-editor-container">
    <!-- 编辑器头部 -->
    <div class="editor-header">
      <div class="editor-title">
        <svg width="16" height="16" viewBox="0 0 24 24">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        在线代码编辑器
      </div>
      <div class="editor-actions">
        <button class="action-btn" @click="copyCode" title="复制代码">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
        </button>
        <button class="action-btn close-btn" @click="$emit('close')" title="关闭编辑器">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 简化的编辑器 -->
    <div class="editor-content">
      <div class="simple-editor">
        <div class="editor-hint">
          <p>💡 这是一个简化的代码编辑器</p>
          <p>🚀 您可以在这里查看和编辑代码</p>
        </div>
        <textarea
          v-model="editableCode"
          class="code-textarea"
          spellcheck="false"
          @keydown="onKeyDown"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

// Props
const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
defineEmits(['close'])

// 响应式数据
const editableCode = ref('')

// 初始化代码
const initCode = () => {
  editableCode.value = props.code || getDefaultCode()
}

// 获取默认代码
const getDefaultCode = () => {
  return `<template>
  <div class="demo-container">
    <h2>Helper Components 示例</h2>

    <div class="component-showcase">
      <div class="helper-toast success">
        ✅ 这是一个成功提示
      </div>

      <button class="copy-btn">
        复制按钮
      </button>

      <img
        src="https://via.placeholder.com/200x150?text=示例图片"
        alt="示例图片"
        class="helper-image"
      />

      <button @click="showDialog = !showDialog">
        {{ showDialog ? '隐藏' : '显示' }}对话框
      </button>

      <div v-if="showDialog" class="helper-dialog">
        <h3>示例对话框</h3>
        <p>这是一个使用 helper-components 的示例</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const showDialog = ref(false)
<\/script>

<style scoped>
.demo-container {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.component-showcase {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 400px;
}

.helper-toast {
  padding: 12px 16px;
  border-radius: 4px;
  margin: 8px 0;
}

.helper-toast.success {
  background: #67c23a;
  color: white;
}

.copy-btn, button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.copy-btn:hover, button:hover {
  background: #66b1ff;
}

.helper-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.helper-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1000;
}
</style>`
}

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(editableCode.value)
    // 显示复制成功提示
    const btn = document.querySelector('[title="复制代码"]')
    if (btn) {
      const originalTitle = btn.title
      btn.title = '✅ 复制成功!'
      setTimeout(() => {
        btn.title = originalTitle
      }, 2000)
    }
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 键盘事件处理
const onKeyDown = (e) => {
  if (e.key === 'Tab') {
    e.preventDefault()
    const start = e.target.selectionStart
    const end = e.target.selectionEnd
    e.target.value = e.target.value.substring(0, start) + '  ' + e.target.value.substring(end)
    e.target.selectionStart = e.target.selectionEnd = start + 2
    editableCode.value = e.target.value
  }
}

// 初始化
if (props.visible) {
  initCode()
}
</script>

<style scoped>
.code-editor-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--vp-c-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--vp-c-text-1);
  font-size: 16px;
}

.editor-title svg {
  color: rgb(94, 130, 192);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 6px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-text-1);
  border-color: rgb(94, 130, 192);
}

.close-btn:hover {
  background: #fee;
  color: #c33;
  border-color: #fcc;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

/* 覆盖 Vue REPL 的默认样式 */
:deep(.vue-repl) {
  height: 100% !important;
  border: none !important;
}

:deep(.vue-repl .split-pane) {
  height: 100% !important;
}

:deep(.vue-repl .editor-container) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container) {
  background: var(--vp-c-bg) !important;
}

:deep(.vue-repl .file-selector) {
  background: var(--vp-c-bg-soft) !important;
  border-bottom: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file) {
  color: var(--vp-c-text-2) !important;
  border-right: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file.active) {
  background: var(--vp-c-bg) !important;
  color: var(--vp-c-text-1) !important;
}

:deep(.vue-repl .monaco-editor) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container iframe) {
  background: white !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    padding: 8px 12px;
  }

  .editor-title {
    font-size: 14px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  :deep(.vue-repl) {
    --split-pane-min-size: 100px !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :deep(.vue-repl .output-container iframe) {
    background: #1a1a1a !important;
  }
}
</style>
