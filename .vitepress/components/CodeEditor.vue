<template>
  <div class="code-editor-container">
    <!-- 编辑器头部 -->
    <div class="editor-header">
      <div class="editor-title">
        <svg width="16" height="16" viewBox="0 0 24 24">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        在线代码编辑器
      </div>
      <div class="editor-actions">
        <button class="action-btn" @click="resetCode" title="重置代码">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
          </svg>
        </button>
        <button class="action-btn" @click="copyCode" title="复制代码">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
        </button>
        <button class="action-btn close-btn" @click="$emit('close')" title="关闭编辑器">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- REPL 编辑器 -->
    <div class="editor-content">
      <Repl
        ref="replRef"
        :store="store"
        :show-compile-output="false"
        :show-import-map="false"
        :clear-console="false"
        :layout="layout"
        :ssr="false"
        @keydown="onKeydown"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { Repl, ReplStore } from '@vue/repl'
import '@vue/repl/style.css'

// Props
const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close', 'code-change'])

// 响应式数据
const replRef = ref()
const store = ref()
const layout = ref('horizontal')
const originalCode = ref('')

// 初始化 REPL Store
const initStore = () => {
  store.value = new ReplStore({
    showOutput: true,
    outputMode: 'preview'
  })

  // 设置 Vue 版本
  store.value.setVueVersion('3.4.0')

  // 添加 helper-components 依赖
  store.value.setImportMap({
    imports: {
      'vue': 'https://unpkg.com/vue@3.4.0/dist/vue.esm-browser.js',
      '@vue/shared': 'https://unpkg.com/@vue/shared@3.4.0/dist/shared.esm-bundler.js'
    }
  })

  // 设置初始文件
  setupInitialFiles()
}

// 设置初始文件
const setupInitialFiles = () => {
  // 清除默认文件
  store.value.files = {}

  // 添加主文件
  const mainFile = 'App.vue'
  const code = props.code || getDefaultCode()
  
  store.value.addFile(mainFile, code)
  store.value.setActive(mainFile)
  
  // 保存原始代码
  originalCode.value = code

  // 添加 helper-components 类型定义和组件
  addHelperComponents()
}

// 添加 helper-components 组件
const addHelperComponents = () => {
  // 添加组件库的类型定义文件
  store.value.addFile('helper-components.d.ts', `
declare module 'helper-components' {
  import { DefineComponent } from 'vue'
  
  export const HelperDialog: DefineComponent<any, any, any>
  export const HelperImage: DefineComponent<any, any, any>
  export const HelperToast: DefineComponent<any, any, any>
  export const HelperSelect: DefineComponent<any, any, any>
  export const HelperRadio: DefineComponent<any, any, any>
  export const HelperNodata: DefineComponent<any, any, any>
  export const HelperIconTips: DefineComponent<any, any, any>
  export const ButtonCopy: DefineComponent<any, any, any>
  export const Loading: DefineComponent<any, any, any>
  export const Carousel: DefineComponent<any, any, any>
  export const AudioPlayer: DefineComponent<any, any, any>
  export const VideoPlayer: DefineComponent<any, any, any>
  export const LiveList: DefineComponent<any, any, any>
  export const VideoList: DefineComponent<any, any, any>
  export const AgeTips: DefineComponent<any, any, any>
}
`)

  // 添加组件库模拟文件
  store.value.addFile('helper-components.js', `
// Helper Components 模拟实现
import { defineComponent, h } from 'vue'

// 基础组件模拟
export const HelperDialog = defineComponent({
  name: 'HelperDialog',
  props: {
    visible: Boolean,
    title: String,
    content: String
  },
  setup(props, { slots }) {
    return () => props.visible ? h('div', {
      style: {
        position: 'fixed',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        background: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
        zIndex: 1000
      }
    }, [
      h('h3', props.title || '对话框'),
      h('p', props.content || '这是一个模拟的 HelperDialog 组件'),
      slots.default?.()
    ]) : null
  }
})

export const HelperImage = defineComponent({
  name: 'HelperImage',
  props: {
    src: String,
    alt: String,
    lazy: Boolean
  },
  setup(props) {
    return () => h('img', {
      src: props.src || 'https://via.placeholder.com/300x200?text=HelperImage',
      alt: props.alt || 'Helper Image',
      style: {
        maxWidth: '100%',
        height: 'auto',
        borderRadius: '4px'
      }
    })
  }
})

export const HelperToast = defineComponent({
  name: 'HelperToast',
  props: {
    message: String,
    type: { type: String, default: 'info' }
  },
  setup(props) {
    const colors = {
      info: '#409eff',
      success: '#67c23a',
      warning: '#e6a23c',
      error: '#f56c6c'
    }
    
    return () => h('div', {
      style: {
        padding: '12px 16px',
        background: colors[props.type] || colors.info,
        color: 'white',
        borderRadius: '4px',
        margin: '8px 0'
      }
    }, props.message || '这是一个模拟的 HelperToast 组件')
  }
})

export const ButtonCopy = defineComponent({
  name: 'ButtonCopy',
  props: {
    text: String,
    size: String
  },
  setup(props) {
    const handleCopy = () => {
      navigator.clipboard?.writeText(props.text || '复制内容')
      alert('复制成功！')
    }
    
    return () => h('button', {
      onClick: handleCopy,
      style: {
        padding: '8px 16px',
        background: '#409eff',
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer'
      }
    }, '复制')
  }
})

export const Loading = defineComponent({
  name: 'Loading',
  props: {
    size: String,
    color: String
  },
  setup(props) {
    return () => h('div', {
      style: {
        display: 'inline-block',
        width: '20px',
        height: '20px',
        border: '2px solid #f3f3f3',
        borderTop: '2px solid ' + (props.color || '#409eff'),
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }
    })
  }
})

// 导出所有组件
export default {
  HelperDialog,
  HelperImage,
  HelperToast,
  ButtonCopy,
  Loading
}
`)
}

// 获取默认代码
const getDefaultCode = () => {
  return `<template>
  <div class="demo-container">
    <h2>Helper Components 示例</h2>
    
    <div class="component-showcase">
      <HelperImage 
        src="https://via.placeholder.com/200x150?text=示例图片"
        alt="示例图片"
      />
      
      <HelperToast 
        message="这是一个成功提示"
        type="success"
      />
      
      <ButtonCopy 
        :text="copyText"
      />
      
      <button @click="showDialog = !showDialog">
        {{ showDialog ? '隐藏' : '显示' }}对话框
      </button>
      
      <HelperDialog 
        :visible="showDialog"
        title="示例对话框"
        content="这是一个使用 helper-components 的示例"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  HelperDialog, 
  HelperImage, 
  HelperToast, 
  ButtonCopy 
} from './helper-components.js'

const showDialog = ref(false)
const copyText = ref('Hello Helper Components!')
</script>

<style scoped>
.demo-container {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.component-showcase {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 400px;
}

button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

button:hover {
  background: #66b1ff;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>`
}

// 重置代码
const resetCode = () => {
  if (store.value && originalCode.value) {
    store.value.setFiles({
      'App.vue': originalCode.value
    })
    store.value.setActive('App.vue')
  }
}

// 复制代码
const copyCode = async () => {
  try {
    const activeFile = store.value?.state.activeFile
    if (activeFile) {
      await navigator.clipboard.writeText(activeFile.code)
      // 显示复制成功提示
      const btn = document.querySelector('[title="复制代码"]')
      if (btn) {
        const originalTitle = btn.title
        btn.title = '✅ 复制成功!'
        setTimeout(() => {
          btn.title = originalTitle
        }, 2000)
      }
    }
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 键盘事件处理
const onKeydown = (e) => {
  // Ctrl/Cmd + S 保存
  if ((e.ctrlKey || e.metaKey) && e.key === 's') {
    e.preventDefault()
    // 这里可以添加保存逻辑
  }
}

// 监听代码变化
watch(() => props.code, (newCode) => {
  if (newCode && store.value) {
    setupInitialFiles()
  }
}, { immediate: false })

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible && !store.value) {
    initStore()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    initStore()
  }
})
</script>

<style scoped>
.code-editor-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--vp-c-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--vp-c-text-1);
  font-size: 16px;
}

.editor-title svg {
  color: rgb(94, 130, 192);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 6px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-text-1);
  border-color: rgb(94, 130, 192);
}

.close-btn:hover {
  background: #fee;
  color: #c33;
  border-color: #fcc;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

/* 覆盖 Vue REPL 的默认样式 */
:deep(.vue-repl) {
  height: 100% !important;
  border: none !important;
}

:deep(.vue-repl .split-pane) {
  height: 100% !important;
}

:deep(.vue-repl .editor-container) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container) {
  background: var(--vp-c-bg) !important;
}

:deep(.vue-repl .file-selector) {
  background: var(--vp-c-bg-soft) !important;
  border-bottom: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file) {
  color: var(--vp-c-text-2) !important;
  border-right: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file.active) {
  background: var(--vp-c-bg) !important;
  color: var(--vp-c-text-1) !important;
}

:deep(.vue-repl .monaco-editor) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container iframe) {
  background: white !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    padding: 8px 12px;
  }

  .editor-title {
    font-size: 14px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  :deep(.vue-repl) {
    --split-pane-min-size: 100px !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :deep(.vue-repl .output-container iframe) {
    background: #1a1a1a !important;
  }
}
</style>
