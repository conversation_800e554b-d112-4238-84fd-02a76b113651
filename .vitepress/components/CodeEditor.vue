<template>
  <div class="code-editor-container">
    <!-- 编辑器头部 -->
    <div class="editor-header">
      <div class="editor-title">
        <svg width="16" height="16" viewBox="0 0 24 24">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        Vue 在线编辑器
      </div>
      <div class="editor-actions">
        <button class="action-btn" @click="resetCode" title="重置代码">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
          </svg>
        </button>
        <button class="action-btn" @click="copyCode" title="复制代码">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
          </svg>
        </button>
        <button class="action-btn close-btn" @click="$emit('close')" title="关闭编辑器">
          <svg width="14" height="14" viewBox="0 0 24 24">
            <path fill="currentColor" d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Vue REPL 编辑器 -->
    <div class="editor-content">
      <Repl
        v-if="store"
        :store="store"
        :show-compile-output="false"
        :show-import-map="false"
        :clear-console="false"
        :layout="'horizontal'"
        :ssr="false"
        class="vue-repl"
      />
      <div v-else class="loading-editor">
        <div class="loading-spinner"></div>
        <p>正在加载编辑器...</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { Repl, ReplStore } from '@vue/repl'
import '@vue/repl/style.css'

// Props
const props = defineProps({
  code: {
    type: String,
    default: ''
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
defineEmits(['close'])

// 响应式数据
const store = ref(null)
const originalCode = ref('')

// 初始化 REPL Store
const initStore = async () => {
  try {
    // 创建 REPL Store
    const replStore = new ReplStore({
      showOutput: true,
      outputMode: 'preview'
    })

    // 设置 Vue 版本
    await replStore.setVueVersion('3.4.0')

    // 设置 import map
    replStore.setImportMap({
      imports: {
        'vue': 'https://unpkg.com/vue@3.4.0/dist/vue.esm-browser.js'
      }
    })

    // 设置初始文件
    const code = props.code || getDefaultCode()
    originalCode.value = code

    // 清除默认文件并添加我们的文件
    replStore.files = {}
    replStore.addFile('App.vue', code)
    replStore.setActive('App.vue')

    store.value = replStore
  } catch (error) {
    console.error('初始化 REPL 失败:', error)
  }
}

// 获取默认代码
const getDefaultCode = () => {
  return `<template>
  <div class="demo-container">
    <h2>Helper Components 示例</h2>

    <div class="component-showcase">
      <div class="helper-toast success">
        ✅ 这是一个成功提示
      </div>

      <button class="copy-btn" @click="copyText">
        复制按钮
      </button>

      <img
        src="https://via.placeholder.com/200x150?text=示例图片"
        alt="示例图片"
        class="helper-image"
      />

      <button @click="showDialog = !showDialog">
        {{ showDialog ? '隐藏' : '显示' }}对话框
      </button>

      <div v-if="showDialog" class="helper-dialog">
        <h3>示例对话框</h3>
        <p>这是一个使用 helper-components 的示例</p>
        <button @click="showDialog = false">关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const showDialog = ref(false)

const copyText = () => {
  alert('复制功能演示')
}
<\/script>

<style scoped>
.demo-container {
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.component-showcase {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 400px;
}

.helper-toast {
  padding: 12px 16px;
  border-radius: 4px;
  margin: 8px 0;
}

.helper-toast.success {
  background: #67c23a;
  color: white;
}

.copy-btn, button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.copy-btn:hover, button:hover {
  background: #66b1ff;
}

.helper-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.helper-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 1000;
  border: 1px solid #ddd;
}
</style>`
}

// 重置代码
const resetCode = () => {
  if (store.value && originalCode.value) {
    store.value.files = {}
    store.value.addFile('App.vue', originalCode.value)
    store.value.setActive('App.vue')
  }
}

// 复制代码
const copyCode = async () => {
  try {
    const activeFile = store.value?.state.activeFile
    if (activeFile) {
      await navigator.clipboard.writeText(activeFile.code)
      // 显示复制成功提示
      const btn = document.querySelector('[title="复制代码"]')
      if (btn) {
        const originalTitle = btn.title
        btn.title = '✅ 复制成功!'
        setTimeout(() => {
          btn.title = originalTitle
        }, 2000)
      }
    }
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 监听可见性变化
watch(() => props.visible, (visible) => {
  if (visible && !store.value) {
    initStore()
  }
})

// 生命周期
onMounted(() => {
  if (props.visible) {
    initStore()
  }
})
</script>

<style scoped>
.code-editor-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--vp-c-bg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.editor-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--vp-c-text-1);
  font-size: 16px;
}

.editor-title svg {
  color: rgb(94, 130, 192);
}

.editor-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 6px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--vp-c-bg-mute);
  color: var(--vp-c-text-1);
  border-color: rgb(94, 130, 192);
}

.close-btn:hover {
  background: #fee;
  color: #c33;
  border-color: #fcc;
}

.editor-content {
  flex: 1;
  overflow: hidden;
}

.vue-repl {
  height: 100% !important;
  border: none !important;
}

.loading-editor {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--vp-c-text-2);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--vp-c-border);
  border-top: 3px solid rgb(94, 130, 192);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 覆盖 Vue REPL 的默认样式 */
:deep(.vue-repl) {
  height: 100% !important;
  border: none !important;
}

:deep(.vue-repl .split-pane) {
  height: 100% !important;
}

:deep(.vue-repl .editor-container) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container) {
  background: var(--vp-c-bg) !important;
}

:deep(.vue-repl .file-selector) {
  background: var(--vp-c-bg-soft) !important;
  border-bottom: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file) {
  color: var(--vp-c-text-2) !important;
  border-right: 1px solid var(--vp-c-divider) !important;
}

:deep(.vue-repl .file-selector .file.active) {
  background: var(--vp-c-bg) !important;
  color: var(--vp-c-text-1) !important;
}

:deep(.vue-repl .monaco-editor) {
  background: var(--vp-code-block-bg) !important;
}

:deep(.vue-repl .output-container iframe) {
  background: white !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .editor-header {
    padding: 8px 12px;
  }

  .editor-title {
    font-size: 14px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  :deep(.vue-repl) {
    --split-pane-min-size: 100px !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :deep(.vue-repl .output-container iframe) {
    background: #1a1a1a !important;
  }
}
</style>
