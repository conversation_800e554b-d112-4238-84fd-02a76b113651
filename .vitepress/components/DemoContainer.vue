<template>
  <div class="demo-containers">
    <!-- 预览区域 -->
    <div class="demo-preview">
      <div class="demo-content">
        <!-- 实时渲染的组件 -->
        <component v-if="compiledComponent" :is="compiledComponent" />
        <!-- 默认插槽内容 -->
        <slot v-else name="demo" />
      </div>
    </div>

    <!-- 代码编辑区域 -->
    <div v-if="showCode" class="demo-code">
      <div class="demo-code-header">
        <div class="demo-code-tabs">
          <button
            class="demo-tab"
            :class="{ 'active': !isEditing }"
            @click="isEditing = false"
          >
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            只读
          </button>
          <button
            class="demo-tab"
            :class="{ 'active': isEditing }"
            @click="toggleEdit"
          >
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
            编辑
          </button>
        </div>
        <div class="demo-code-actions">
          <button v-if="isEditing" class="demo-action-btn" @click="resetCode" title="重置代码">
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
            </svg>
          </button>
          <button v-if="isEditing" class="demo-action-btn" @click="formatCode" title="格式化代码">
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M4 3h16c.55 0 1 .45 1 1s-.45 1-1 1H4c-.55 0-1-.45-1-1s.45-1 1-1zm0 4h16c.55 0 1 .45 1 1s-.45 1-1 1H4c-.55 0-1-.45-1-1s.45-1 1-1zm0 4h16c.55 0 1 .45 1 1s-.45 1-1 1H4c-.55 0-1-.45-1-1s.45-1 1-1zm0 4h16c.55 0 1 .45 1 1s-.45 1-1 1H4c-.55 0-1-.45-1-1s.45-1 1-1z"/>
            </svg>
          </button>
        </div>
      </div>

      <div class="demo-code-content">
        <!-- 只读模式 -->
        <div v-if="!isEditing" class="demo-code-readonly">
          <slot name="code" />
        </div>

        <!-- 编辑模式 -->
        <div v-else class="demo-code-editor">
          <div ref="editorContainer" class="monaco-editor-container"></div>
        </div>
      </div>

      <!-- 编译错误提示 -->
      <div v-if="compileError" class="demo-error">
        <div class="demo-error-header">
          <svg width="16" height="16" viewBox="0 0 24 24">
            <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          编译错误
        </div>
        <pre class="demo-error-content">{{ compileError }}</pre>
      </div>
    </div>

    <div v-if="$slots.controls" class="demo-controls">
      <slot name="controls" />
    </div>

    <div class="demo-actions">
      <button
        class="demo-toggle-btn"
        @click="showCode = !showCode"
        :class="{ 'is-active': showCode }"
      >
        <svg class="demo-icon" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        <span>{{ showCode ? '隐藏代码' : '查看代码' }}</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'

// Props
const props = defineProps({
  initialCode: {
    type: String,
    default: ''
  }
})

// 响应式数据
const showCode = ref(false)
const isEditing = ref(false)
const editorContainer = ref(null)
const editableCode = ref('')
const compileError = ref('')
const originalCode = ref('')

// 切换编辑模式
const toggleEdit = async () => {
  isEditing.value = true
  await nextTick()

  if (!editableCode.value) {
    editableCode.value = originalCode.value || props.initialCode
  }

  // 创建简单的代码编辑器
  if (editorContainer.value) {
    createSimpleEditor()
  }
}

// 创建简单编辑器
const createSimpleEditor = () => {
  const textarea = document.createElement('textarea')
  textarea.value = editableCode.value
  textarea.className = 'simple-code-editor'
  textarea.spellcheck = false
  textarea.addEventListener('input', (e) => {
    editableCode.value = e.target.value
    validateCode(e.target.value)
  })

  // 支持 Tab 键缩进
  textarea.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
      e.preventDefault()
      const start = e.target.selectionStart
      const end = e.target.selectionEnd
      e.target.value = e.target.value.substring(0, start) + '  ' + e.target.value.substring(end)
      e.target.selectionStart = e.target.selectionEnd = start + 2
    }
  })

  editorContainer.value.innerHTML = ''
  editorContainer.value.appendChild(textarea)
}

// 验证代码
const validateCode = (code) => {
  try {
    compileError.value = ''

    // 简单的语法检查
    if (code.includes('<template>') && !code.includes('</template>')) {
      compileError.value = '缺少 </template> 标签'
      return
    }

    if (code.includes('<script>') && !code.includes('</script>')) {
      compileError.value = '缺少 </script> 标签'
      return
    }

    if (code.includes('<style>') && !code.includes('</style>')) {
      compileError.value = '缺少 </style> 标签'
      return
    }

    // 这里可以添加更多验证逻辑

  } catch (error) {
    compileError.value = error.message
  }
}

// 重置代码
const resetCode = () => {
  editableCode.value = originalCode.value || props.initialCode
  const textarea = editorContainer.value?.querySelector('textarea')
  if (textarea) {
    textarea.value = editableCode.value
  }
  compileError.value = ''
}

// 格式化代码（简单版本）
const formatCode = () => {
  // 简单的格式化逻辑
  let formatted = editableCode.value

  // 基本的缩进处理
  const lines = formatted.split('\n')
  let indentLevel = 0
  const indentSize = 2

  const formattedLines = lines.map(line => {
    const trimmed = line.trim()
    if (!trimmed) return ''

    // 减少缩进
    if (trimmed.startsWith('</') || trimmed.startsWith('}')) {
      indentLevel = Math.max(0, indentLevel - 1)
    }

    const result = ' '.repeat(indentLevel * indentSize) + trimmed

    // 增加缩进
    if (trimmed.includes('<') && !trimmed.includes('</') && !trimmed.endsWith('/>') ||
        trimmed.endsWith('{')) {
      indentLevel++
    }

    return result
  })

  editableCode.value = formattedLines.join('\n')
  const textarea = editorContainer.value?.querySelector('textarea')
  if (textarea) {
    textarea.value = editableCode.value
  }
}

// 获取原始代码
const extractOriginalCode = () => {
  // 从插槽中提取原始代码
  const codeSlot = document.querySelector('.demo-code-readonly pre code')
  if (codeSlot) {
    originalCode.value = codeSlot.textContent || ''
  }
}

// 生命周期
onMounted(() => {
  extractOriginalCode()
})

// 监听显示代码状态
watch(showCode, async (newVal) => {
  if (newVal) {
    await nextTick()
    extractOriginalCode()
  }
})
</script>

<style scoped>
.demo-containers {
  border: 1px solid var(--vp-c-border);
  border-radius: 12px;
  overflow: hidden;
  margin: 24px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.2s ease;
}

.demo-containers:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.demo-preview {
  background-color: var(--vp-c-bg);
  position: relative;
}

.demo-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  background: linear-gradient(135deg, var(--vp-c-bg) 0%, var(--vp-c-bg-soft) 100%);
}

.demo-code {
  background-color: var(--vp-code-block-bg);
  border-top: 1px solid var(--vp-c-divider);
}

.demo-code-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 代码编辑器头部 */
.demo-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--vp-c-bg-mute);
  border-bottom: 1px solid var(--vp-c-divider);
}

.demo-code-tabs {
  display: flex;
  gap: 4px;
}

.demo-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 6px;
  color: var(--vp-c-text-2);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.demo-tab:hover {
  background: var(--vp-c-bg-soft);
  color: var(--vp-c-text-1);
}

.demo-tab.active {
  background: var(--vp-c-brand-1);
  color: white;
  border-color: var(--vp-c-brand-1);
}

.demo-code-actions {
  display: flex;
  gap: 4px;
}

.demo-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  color: var(--vp-c-text-2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.demo-action-btn:hover {
  background: var(--vp-c-bg-soft);
  color: var(--vp-c-text-1);
  border-color: var(--vp-c-brand-1);
}

/* Monaco 编辑器容器 */
.monaco-editor-container {
  height: 400px;
  width: 100%;
}

.demo-code-readonly {
  max-height: 400px;
  overflow-y: auto;
}

.demo-code-editor {
  position: relative;
}

/* 错误提示 */
.demo-error {
  background: #fee;
  border-top: 1px solid #fcc;
  color: #c33;
}

.demo-error-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  font-weight: 500;
  font-size: 14px;
  background: #fdd;
  border-bottom: 1px solid #fcc;
}

.demo-error-content {
  padding: 12px 16px;
  margin: 0;
  font-family: var(--vp-font-family-mono);
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  overflow-x: auto;
}

.demo-actions {
  padding: 0;
  background: linear-gradient(90deg, var(--vp-c-bg-soft) 0%, var(--vp-c-bg-mute) 100%);
  border-top: 1px solid var(--vp-c-divider);
  position: relative;
}

.demo-toggle-btn {
  width: 100%;
  padding: 3px 20px;
  background: transparent;
  color: #869dc0;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.demo-preview{
  padding: 0 !important;
}

.demo-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.demo-toggle-btn:hover::before {
  left: 100%;
}

.demo-toggle-btn:hover {
  color:rgb(94, 130, 192);
}

.demo-toggle-btn.is-active {
  color: rgb(94, 130, 192);
}

.demo-toggle-btn.is-active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
}

.demo-icon {
  transition: transform 0.3s ease;
  opacity: 0.8;
}

.demo-toggle-btn:hover .demo-icon {
  transform: scale(1.1);
  opacity: 1;
}

.demo-toggle-btn.is-active .demo-icon {
  transform: rotate(180deg);
}

/* 滚动条样式 */
.demo-content::-webkit-scrollbar,
.demo-code-content::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

.demo-content::-webkit-scrollbar-track,
.demo-code-content::-webkit-scrollbar-track {
  background: transparent;
}

.demo-content::-webkit-scrollbar-thumb,
.demo-code-content::-webkit-scrollbar-thumb {
  background: var(--vp-custom-block-tip-bg);
  border-radius: 1px;
  transition: background 0.2s ease;
}

.demo-content::-webkit-scrollbar-thumb:hover,
.demo-code-content::-webkit-scrollbar-thumb:hover {
  background: var(--vp-custom-block-tip-bg);
}

/* 滚动条交叉角落 */
.demo-content::-webkit-scrollbar-corner,
.demo-code-content::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
.demo-content,
.demo-code-content {
  scrollbar-width: thin;
  scrollbar-color: var(--vp-custom-block-tip-bg) transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-containers {
    margin: 16px 0;
    border-radius: 8px;
  }

  .demo-content {
    max-height: 300px;
  }

  .demo-code-content {
    max-height: 250px;
  }

  .demo-toggle-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .demo-containers {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .demo-containers:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}
</style>