<template>
  <div class="demo-containers">
    <!-- 预览区域 -->
    <div class="demo-preview">
      <div class="demo-content">
        <slot name="demo" />
      </div>
    </div>

    <!-- 代码区域 -->
    <div v-if="showCode" class="demo-code">
      <!-- 工具栏 -->
      <div class="demo-toolbar">
        <div class="toolbar-left">
          <button
            class="tool-btn"
            :class="{ active: !isEditing }"
            @click="setMode('view')"
          >
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
            </svg>
            查看
          </button>
          <button
            class="tool-btn"
            :class="{ active: isEditing }"
            @click="setMode('edit')"
          >
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
            在线编辑
          </button>
        </div>

        <div class="toolbar-right" v-if="isEditing">
          <button class="tool-btn" @click="openInPlayground" title="在新窗口打开">
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M14,3V5H17.59L7.76,14.83L9.17,16.24L19,6.41V10H21V3M19,19H5V5H12V3H5C3.89,3 3,3.9 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V12H19V19Z"/>
            </svg>
          </button>
          <button class="tool-btn" @click="copyCode" title="复制代码">
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 代码内容 -->
      <div class="demo-code-content">
        <!-- 只读模式 -->
        <div v-if="!isEditing" class="code-readonly">
          <slot name="code" />
        </div>

        <!-- 编辑模式 - Vue REPL -->
        <div v-else class="code-editor">
          <div ref="replContainer" class="repl-container">
            <!-- Vue REPL 将在这里渲染 -->
          </div>
        </div>
      </div>
    </div>

    <!-- 控制区域 -->
    <div v-if="$slots.controls" class="demo-controls">
      <slot name="controls" />
    </div>

    <!-- 切换按钮 -->
    <div class="demo-actions">
      <button
        class="demo-toggle-btn"
        @click="showCode = !showCode"
        :class="{ 'is-active': showCode }"
      >
        <svg class="demo-icon" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        <span>{{ showCode ? '隐藏代码' : '查看代码' }}</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted, watch } from 'vue'

// 响应式数据
const showCode = ref(false)
const isEditing = ref(false)
const replContainer = ref(null)
const originalCode = ref('')

// 设置模式
const setMode = async (mode) => {
  if (mode === 'edit') {
    isEditing.value = true
    await nextTick()
    initializeREPL()
  } else {
    isEditing.value = false
  }
}

// 初始化 Vue REPL
const initializeREPL = async () => {
  if (!replContainer.value) return

  try {
    // 提取原始代码
    extractOriginalCode()

    // 创建简化的在线编辑器
    createSimpleEditor()

  } catch (error) {
    console.error('初始化编辑器失败:', error)
    // 回退到简单的文本编辑器
    createFallbackEditor()
  }
}

// 创建简单编辑器
const createSimpleEditor = () => {
  const container = replContainer.value
  container.innerHTML = ''

  // 创建编辑器容器
  const editorWrapper = document.createElement('div')
  editorWrapper.className = 'simple-editor-wrapper'

  // 创建文本编辑器
  const textarea = document.createElement('textarea')
  textarea.className = 'simple-code-editor'
  textarea.value = originalCode.value
  textarea.spellcheck = false
  textarea.placeholder = '在这里编辑您的 Vue 代码...'

  // 添加语法高亮提示
  const hint = document.createElement('div')
  hint.className = 'editor-hint'
  hint.innerHTML = `
    <div class="hint-item">💡 支持完整的 Vue 3 语法</div>
    <div class="hint-item">🎯 修改代码后可在上方预览区域查看效果</div>
    <div class="hint-item">🚀 点击"在新窗口打开"使用完整的 Vue Playground</div>
  `

  // 支持 Tab 键
  textarea.addEventListener('keydown', (e) => {
    if (e.key === 'Tab') {
      e.preventDefault()
      const start = e.target.selectionStart
      const end = e.target.selectionEnd
      e.target.value = e.target.value.substring(0, start) + '  ' + e.target.value.substring(end)
      e.target.selectionStart = e.target.selectionEnd = start + 2
    }
  })

  editorWrapper.appendChild(hint)
  editorWrapper.appendChild(textarea)
  container.appendChild(editorWrapper)
}

// 创建回退编辑器
const createFallbackEditor = () => {
  const container = replContainer.value
  container.innerHTML = `
    <div class="fallback-editor">
      <div class="fallback-message">
        <h3>🛠️ 简化编辑模式</h3>
        <p>当前环境不支持完整的在线编辑器，您可以：</p>
        <ul>
          <li>查看和复制代码</li>
          <li>在本地环境中编辑</li>
          <li>使用 Vue 官方 Playground</li>
        </ul>
      </div>
      <textarea class="fallback-textarea" readonly>${originalCode.value}</textarea>
    </div>
  `
}

// 提取原始代码
const extractOriginalCode = () => {
  const codeElement = document.querySelector('.code-readonly pre code')
  if (codeElement) {
    originalCode.value = codeElement.textContent || ''
  }
}

// 在 Vue Playground 中打开
const openInPlayground = () => {
  const code = encodeURIComponent(originalCode.value)
  const playgroundUrl = `https://play.vuejs.org/#eNp9kUFuwjAQRa8y8roqSQiNu6PcgG5YVN2MnUlqNbGNPROCEHfvOIGWLivLM+/9/2dsXxjsfxVr9k3vKKyRdIqtVscmaNdLhsoVZSTOOAcTIBjXWRKELuyM7OY1QJGKD1XVvQeJsWGNAw==&${code}`
  window.open(playgroundUrl, '_blank')
}

// 复制代码
const copyCode = async () => {
  try {
    await navigator.clipboard.writeText(originalCode.value)
    // 可以添加复制成功的提示
    showCopySuccess()
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 显示复制成功提示
const showCopySuccess = () => {
  const button = document.querySelector('[title="复制代码"]')
  if (button) {
    const originalTitle = button.title
    button.title = '✅ 复制成功!'
    setTimeout(() => {
      button.title = originalTitle
    }, 2000)
  }
}

// 生命周期
onMounted(() => {
  // 监听代码显示状态变化
  const unwatchShowCode = watch(showCode, async (newVal) => {
    if (newVal) {
      await nextTick()
      extractOriginalCode()
    }
  })

  // 组件卸载时清理
  onUnmounted(() => {
    unwatchShowCode()
  })
})
</script>

<style scoped>
.demo-containers {
  border: 1px solid var(--vp-c-border);
  border-radius: 12px;
  overflow: hidden;
  margin: 24px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.2s ease;
}

.demo-containers:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.demo-preview {
  background-color: var(--vp-c-bg);
  position: relative;
}

.demo-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  /* background: linear-gradient(135deg, var(--vp-c-bg) 0%, var(--vp-c-bg-soft) 100%); */
}

.demo-code {
  background-color: var(--vp-code-block-bg);
  border-top: 1px solid var(--vp-c-divider);
}

.demo-code-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 工具栏样式 */
.demo-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--vp-c-bg-mute);
  border-bottom: 1px solid var(--vp-c-divider);
}

.toolbar-left,
.toolbar-right {
  display: flex;
  gap: 4px;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 6px;
  color: var(--vp-c-text-2);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background: var(--vp-c-bg-soft);
  color: var(--vp-c-text-1);
  border-color: rgb(94, 130, 192);
}

.tool-btn.active {
  background: rgb(94, 130, 192);
  color: white;
  border-color: rgb(94, 130, 192);
}

/* 编辑器样式 */
.code-readonly {
  max-height: 500px;
  overflow-y: auto;
}

.code-editor {
  position: relative;
}

.repl-container {
  min-height: 400px;
  background: var(--vp-code-block-bg);
}

/* 简单编辑器样式 */
.simple-editor-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-hint {
  padding: 12px 16px;
  background: var(--vp-c-bg-soft);
  border-bottom: 1px solid var(--vp-c-divider);
  font-size: 12px;
}

.hint-item {
  margin: 4px 0;
  color: var(--vp-c-text-2);
}

.simple-code-editor {
  flex: 1;
  min-height: 300px;
  padding: 16px;
  border: none;
  outline: none;
  background: var(--vp-code-block-bg);
  color: var(--vp-c-text-1);
  font-family: var(--vp-font-family-mono);
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  tab-size: 2;
}

.simple-code-editor:focus {
  background: var(--vp-code-block-bg);
}

/* 回退编辑器样式 */
.fallback-editor {
  padding: 16px;
  background: var(--vp-code-block-bg);
}

.fallback-message {
  margin-bottom: 16px;
  padding: 16px;
  background: var(--vp-c-bg-soft);
  border-radius: 8px;
  border-left: 4px solid rgb(94, 130, 192);
}

.fallback-message h3 {
  margin: 0 0 8px 0;
  color: var(--vp-c-text-1);
}

.fallback-message p {
  margin: 8px 0;
  color: var(--vp-c-text-2);
}

.fallback-message ul {
  margin: 8px 0;
  padding-left: 20px;
  color: var(--vp-c-text-2);
}

.fallback-textarea {
  width: 100%;
  height: 200px;
  padding: 12px;
  border: 1px solid var(--vp-c-border);
  border-radius: 4px;
  background: var(--vp-c-bg);
  color: var(--vp-c-text-1);
  font-family: var(--vp-font-family-mono);
  font-size: 13px;
  line-height: 1.4;
  resize: vertical;
}

.demo-actions {
  padding: 0;
  background: linear-gradient(90deg, var(--vp-c-bg-soft) 0%, var(--vp-c-bg-mute) 100%);
  border-top: 1px solid var(--vp-c-divider);
  position: relative;
}

.demo-toggle-btn {
  width: 100%;
  padding: 3px 20px;
  background: transparent;
  color: #869dc0;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.demo-preview{
  padding: 0 !important;
}

.demo-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.demo-toggle-btn:hover::before {
  left: 100%;
}

.demo-toggle-btn:hover {
  color:rgb(94, 130, 192);
}

.demo-toggle-btn.is-active {
  color: rgb(94, 130, 192);
}

.demo-toggle-btn.is-active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
}

.demo-icon {
  transition: transform 0.3s ease;
  opacity: 0.8;
}

.demo-toggle-btn:hover .demo-icon {
  transform: scale(1.1);
  opacity: 1;
}

.demo-toggle-btn.is-active .demo-icon {
  transform: rotate(180deg);
}

/* 滚动条样式 */
.demo-content::-webkit-scrollbar,
.demo-code-content::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

.demo-content::-webkit-scrollbar-track,
.demo-code-content::-webkit-scrollbar-track {
  background: transparent;
}

.demo-content::-webkit-scrollbar-thumb,
.demo-code-content::-webkit-scrollbar-thumb {
  background: var(--vp-custom-block-tip-bg);
  border-radius: 1px;
  transition: background 0.2s ease;
}

.demo-content::-webkit-scrollbar-thumb:hover,
.demo-code-content::-webkit-scrollbar-thumb:hover {
  background: var(--vp-custom-block-tip-bg);
}

/* 滚动条交叉角落 */
.demo-content::-webkit-scrollbar-corner,
.demo-code-content::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
.demo-content,
.demo-code-content {
  scrollbar-width: thin;
  scrollbar-color: var(--vp-custom-block-tip-bg) transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-containers {
    margin: 16px 0;
    border-radius: 8px;
  }

  .demo-content {
    max-height: 300px;
  }

  .demo-code-content {
    max-height: 250px;
  }

  .demo-toggle-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .demo-containers {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .demo-containers:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}
</style>