<template>
  <div class="demo-containers">
    <!-- 预览区域 -->
    <div class="demo-preview">
      <div class="demo-content">
        <slot name="demo" />
      </div>
    </div>

    <!-- 代码区域 -->
    <div v-if="showCode" class="demo-code">
      <!-- 工具栏 -->
      <div class="demo-toolbar">
        <div class="toolbar-left">
          <span class="toolbar-title">
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
            </svg>
            源代码
          </span>
        </div>

        <div class="toolbar-right">
          <button class="tool-btn" @click="openCodeEditor" title="在线编辑器">
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
            </svg>
            编辑
          </button>
          <button class="tool-btn" @click="copyCode" title="复制代码">
            <svg width="14" height="14" viewBox="0 0 24 24">
              <path fill="currentColor" d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- 代码内容 -->
      <div class="demo-code-content">
        <slot name="code" />
      </div>
    </div>

    <!-- 控制区域 -->
    <div v-if="$slots.controls" class="demo-controls">
      <slot name="controls" />
    </div>

    <!-- 切换按钮 -->
    <div class="demo-actions">
      <button
        class="demo-toggle-btn"
        @click="showCode = !showCode"
        :class="{ 'is-active': showCode }"
      >
        <svg class="demo-icon" viewBox="0 0 24 24" width="16" height="16">
          <path fill="currentColor" d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
        </svg>
        <span>{{ showCode ? '隐藏代码' : '查看代码' }}</span>
      </button>
    </div>

    <!-- 代码编辑器 -->
    <CodeEditor
      v-if="editorVisible"
      :visible="editorVisible"
      :code="extractedCode"
      @close="closeEditor"
    />
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import CodeEditor from './CodeEditor.vue'

// 响应式数据
const showCode = ref(false)
const editorVisible = ref(false)
const extractedCode = ref('')

// 打开代码编辑器
const openCodeEditor = async () => {
  // 提取代码
  await nextTick()
  const codeElement = document.querySelector('.demo-code-content pre code')
  if (codeElement) {
    extractedCode.value = codeElement.textContent || ''
  }

  // 显示编辑器
  editorVisible.value = true
}

// 关闭编辑器
const closeEditor = () => {
  editorVisible.value = false
}

// 复制代码
const copyCode = async () => {
  try {
    const codeElement = document.querySelector('.demo-code-content pre code')
    if (codeElement) {
      const code = codeElement.textContent || ''
      await navigator.clipboard.writeText(code)

      // 显示复制成功提示
      const btn = document.querySelector('[title="复制代码"]')
      if (btn) {
        const originalTitle = btn.title
        btn.title = '✅ 复制成功!'
        setTimeout(() => {
          btn.title = originalTitle
        }, 2000)
      }
    }
  } catch (err) {
    console.error('复制失败:', err)
  }
}
</script>

<style scoped>
.demo-containers {
  border: 1px solid var(--vp-c-border);
  border-radius: 12px;
  overflow: hidden;
  margin: 24px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: box-shadow 0.2s ease;
}

.demo-containers:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.demo-preview {
  background-color: var(--vp-c-bg);
  position: relative;
}

.demo-content {
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  /* background: linear-gradient(135deg, var(--vp-c-bg) 0%, var(--vp-c-bg-soft) 100%); */
}

.demo-code {
  background-color: var(--vp-code-block-bg);
  border-top: 1px solid var(--vp-c-divider);
}

.demo-code-content {
  max-height: 500px;
  overflow-y: auto;
}

/* 工具栏样式 */
.demo-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: var(--vp-c-bg-mute);
  border-bottom: 1px solid var(--vp-c-divider);
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: var(--vp-c-text-2);
}

.toolbar-title svg {
  color: rgb(94, 130, 192);
}

.toolbar-right {
  display: flex;
  gap: 4px;
}

.tool-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: transparent;
  border: 1px solid var(--vp-c-border);
  border-radius: 6px;
  color: var(--vp-c-text-2);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tool-btn:hover {
  background: var(--vp-c-bg-soft);
  color: var(--vp-c-text-1);
  border-color: rgb(94, 130, 192);
  transform: translateY(-1px);
}

.tool-btn:active {
  transform: translateY(0);
}

.demo-actions {
  padding: 0;
  background: linear-gradient(90deg, var(--vp-c-bg-soft) 0%, var(--vp-c-bg-mute) 100%);
  border-top: 1px solid var(--vp-c-divider);
  position: relative;
}

.demo-toggle-btn {
  width: 100%;
  padding: 3px 20px;
  background: transparent;
  color: #869dc0;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.demo-preview{
  padding: 0 !important;
}

.demo-toggle-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.demo-toggle-btn:hover::before {
  left: 100%;
}

.demo-toggle-btn:hover {
  color:rgb(94, 130, 192);
}

.demo-toggle-btn.is-active {
  color: rgb(94, 130, 192);
}

.demo-toggle-btn.is-active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
}

.demo-icon {
  transition: transform 0.3s ease;
  opacity: 0.8;
}

.demo-toggle-btn:hover .demo-icon {
  transform: scale(1.1);
  opacity: 1;
}

.demo-toggle-btn.is-active .demo-icon {
  transform: rotate(180deg);
}

/* 滚动条样式 */
.demo-content::-webkit-scrollbar,
.demo-code-content::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

.demo-content::-webkit-scrollbar-track,
.demo-code-content::-webkit-scrollbar-track {
  background: transparent;
}

.demo-content::-webkit-scrollbar-thumb,
.demo-code-content::-webkit-scrollbar-thumb {
  background: var(--vp-custom-block-tip-bg);
  border-radius: 1px;
  transition: background 0.2s ease;
}

.demo-content::-webkit-scrollbar-thumb:hover,
.demo-code-content::-webkit-scrollbar-thumb:hover {
  background: var(--vp-custom-block-tip-bg);
}

/* 滚动条交叉角落 */
.demo-content::-webkit-scrollbar-corner,
.demo-code-content::-webkit-scrollbar-corner {
  background: transparent;
}

/* Firefox 滚动条样式 */
.demo-content,
.demo-code-content {
  scrollbar-width: thin;
  scrollbar-color: var(--vp-custom-block-tip-bg) transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-containers {
    margin: 16px 0;
    border-radius: 8px;
  }

  .demo-content {
    max-height: 300px;
  }

  .demo-code-content {
    max-height: 250px;
  }

  .demo-toggle-btn {
    padding: 10px 16px;
    font-size: 13px;
  }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
  .demo-containers {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .demo-containers:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  }
}
</style>