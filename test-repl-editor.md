# Vue REPL 编辑器测试

这是一个测试页面，用于验证集成了 Vue REPL 的代码编辑器功能。

## 基础示例

<DemoContainer>
  <template #demo>
    <div style="padding: 20px; text-align: center; border: 2px dashed #42b883; border-radius: 8px; background: #f9f9f9;">
      <h3 style="color: #42b883; margin: 0 0 16px 0;">Helper Components 示例</h3>
      <div style="display: flex; flex-direction: column; gap: 12px; align-items: center;">
        <div style="padding: 8px 16px; background: #67c23a; color: white; border-radius: 4px;">
          ✅ 这是一个成功提示
        </div>
        <button style="padding: 8px 16px; background: #409eff; color: white; border: none; border-radius: 4px; cursor: pointer;">
          复制按钮
        </button>
        <img src="https://via.placeholder.com/200x120?text=Helper+Image" alt="示例图片" style="border-radius: 4px;" />
      </div>
    </div>
  </template>
  
  <template #code>
```vue
<template>
  <div class="demo-container">
    <h3>Helper Components 示例</h3>
    
    <div class="component-showcase">
      <HelperToast 
        message="这是一个成功提示"
        type="success"
      />
      
      <ButtonCopy 
        :text="copyText"
      />
      
      <HelperImage 
        src="https://via.placeholder.com/200x120?text=Helper+Image"
        alt="示例图片"
      />
      
      <button @click="showDialog = !showDialog">
        {{ showDialog ? '隐藏' : '显示' }}对话框
      </button>
      
      <HelperDialog 
        :visible="showDialog"
        title="示例对话框"
        content="这是一个使用 helper-components 的示例"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  HelperDialog, 
  HelperImage, 
  HelperToast, 
  ButtonCopy 
} from './helper-components.js'

const showDialog = ref(false)
const copyText = ref('Hello Helper Components!')
</script>

<style scoped>
.demo-container {
  padding: 20px;
  text-align: center;
  border: 2px dashed #42b883;
  border-radius: 8px;
  background: #f9f9f9;
}

.demo-container h3 {
  color: #42b883;
  margin: 0 0 16px 0;
}

.component-showcase {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

button {
  padding: 8px 16px;
  background: #409eff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

button:hover {
  background: #66b1ff;
  transform: translateY(-2px);
}
</style>
```
  </template>
</DemoContainer>

## 功能说明

### ✨ **新功能特性**

1. **Vue REPL 集成**
   - 点击"编辑"按钮打开全屏代码编辑器
   - 基于 Vue 官方 REPL 组件
   - 支持实时预览和编译

2. **Helper Components 预装**
   - 内置了所有 helper-components 组件
   - 提供组件类型定义
   - 模拟组件实现，便于演示

3. **专业编辑体验**
   - Monaco Editor 代码编辑器
   - 语法高亮和智能提示
   - 实时错误检查

### 🛠️ **使用方式**

1. **查看代码**：点击"查看代码"按钮
2. **在线编辑**：点击工具栏中的"编辑"按钮
3. **全屏编辑**：在 Vue REPL 环境中编辑代码
4. **实时预览**：右侧面板实时显示运行结果

### 🎯 **技术特点**

- **完整的 Vue 3 支持**：Composition API、响应式系统
- **组件库集成**：预装 helper-components 所有组件
- **专业工具**：基于 Vue 官方 REPL，功能完整
- **用户友好**：全屏编辑，专注开发体验

这种方案提供了真正专业的在线编辑体验，让用户可以深度体验和学习组件库的使用！
